package com.cdz360.iot.model.pv.vo;

import com.cdz360.base.model.iot.type.EquipStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GtiMonitVo {

    @Schema(description = "逆变器编号")
    private String dno;

    @Schema(description = "逆变器名称")
    private String name;

    @Schema(description = "逆变器SN")
    private String serialNo;

    private String siteId;

    @Schema(description = "站点名称")
    private String siteName;

    @Schema(description = "设备状态")
    private EquipStatus equipStatus;

    @Schema(description = "额定功率")
    private BigDecimal ratedPower;

    @Schema(description = "实时功率")
    private BigDecimal power;

    @Schema(description = "今日发电量")
    private BigDecimal kwh;

    @Schema(description = "等效小时")
    private BigDecimal equivalentTime;

    @Schema(description = "组串容量")
    private Integer stringCapacity;

}
