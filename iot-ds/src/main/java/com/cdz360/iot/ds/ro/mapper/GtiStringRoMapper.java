package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.pv.po.GtiStringPo;
import com.cdz360.iot.model.pv.vo.GtiMonitVo;
import com.cdz360.iot.model.pv.vo.GtiStringPowerVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;

@Mapper
public interface GtiStringRoMapper {

    GtiStringPo getById(@Param("id") Long id);

    List<GtiStringPo> getByGtiDno(@NonNull @Param("gtiDno") String gtiDno,
        @Param("working") Boolean working);

    long getByGtiDnoCount(@NonNull @Param("gtiDno") String gtiDno,
        @Param("working") Boolean working);

    List<GtiStringPowerVo> getByGtiDnoPowerSum(
        @NonNull @Param("gtiDnoList") List<String> gtiDnoList);

    List<GtiMonitVo> getGtiCapacityVo(@NonNull @Param("gtiDnoList") List<String> gtiDnoList);

}
