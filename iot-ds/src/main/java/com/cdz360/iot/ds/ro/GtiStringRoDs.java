package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.GtiStringRoMapper;
import com.cdz360.iot.model.pv.po.GtiStringPo;
import com.cdz360.iot.model.pv.vo.GtiMonitVo;
import com.cdz360.iot.model.pv.vo.GtiStringPowerVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GtiStringRoDs {

    @Autowired
    private GtiStringRoMapper gtiStringRoMapper;

    public GtiStringPo getById(Long id) {
        return this.gtiStringRoMapper.getById(id);
    }

    public List<GtiStringPo> getByGtiDno(String gtiDno, Boolean working) {
        return this.gtiStringRoMapper.getByGtiDno(gtiDno, working);
    }

    public long getByGtiDnoCount(String gtiDno, Boolean working) {
        return this.gtiStringRoMapper.getByGtiDnoCount(gtiDno, working);
    }

    public List<GtiStringPowerVo> getByGtiDnoPowerSum(List<String> gtiDnoList) {
        return this.gtiStringRoMapper.getByGtiDnoPowerSum(gtiDnoList);
    }

    public List<GtiMonitVo> getGtiCapacityVo(List<String> gtiDnoList) {
        return this.gtiStringRoMapper.getGtiCapacityVo(gtiDnoList);
    }

}

