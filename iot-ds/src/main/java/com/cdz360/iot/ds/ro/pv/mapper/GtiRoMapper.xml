<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.pv.mapper.GtiRoMapper">

	<resultMap id="RESULT_GTI_PO" type="com.cdz360.iot.model.pv.po.GtiPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="dno" jdbcType="VARCHAR" property="dno" />
		<result column="name" jdbcType="VARCHAR" property="name" />
		<result column="sid" jdbcType="INTEGER" property="sid" />
		<result column="serialNo" jdbcType="VARCHAR" property="serialNo" />
		<result column="vendor" jdbcType="VARCHAR" property="vendor" />
		<result column="power" jdbcType="BIGINT" property="power" />
		<result column="gwno" jdbcType="VARCHAR" property="gwno" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="deviceModel" jdbcType="VARCHAR" property="deviceModel" />
		<result column="status" property="status" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="alertStatus" property="alertStatus" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />
		<result column="cfgStatus" property="cfgStatus" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="cfgSuccessId" jdbcType="BIGINT" property="cfgSuccessId" />
		<result column="cfgSuccessTime" jdbcType="TIMESTAMP" property="cfgSuccessTime" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<resultMap id="RESULT_GTI_VO" type="com.cdz360.iot.model.pv.vo.GtiVo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="dno" jdbcType="VARCHAR" property="dno" />
		<result column="name" jdbcType="VARCHAR" property="name" />
		<result column="serialNo" jdbcType="VARCHAR" property="serialNo"/>
		<result column="deviceModel" jdbcType="VARCHAR" property="deviceModel"/>
		<result column="sid" jdbcType="INTEGER" property="sid" />
		<result column="vendor" jdbcType="VARCHAR" property="vendor" />
		<result column="com" typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"
			property="com"/>
		<result column="power" jdbcType="BIGINT" property="power"/>
		<result column="outputVoltage" jdbcType="DECIMAL" property="outputVoltage"/>
		<result column="outputCurrent" jdbcType="DECIMAL" property="outputCurrent"/>
		<result column="apparentPower" jdbcType="DECIMAL" property="apparentPower"/>
		<result column="groupNum" jdbcType="INTEGER" property="groupNum" />
		<result column="mpptNum" jdbcType="INTEGER" property="mpptNum"/>
		<result column="gwno" jdbcType="VARCHAR" property="gwno" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="siteName" jdbcType="VARCHAR" property="siteName" />
		<result column="gwName" jdbcType="VARCHAR" property="gwName" />
		<result column="mpptVoltageMin" jdbcType="DECIMAL" property="mpptVoltageMin"/>
		<result column="mpptVoltageMax" jdbcType="DECIMAL" property="mpptVoltageMax"/>
		<result column="status" property="status" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="alertStatus" property="alertStatus" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />
		<result column="cfgStatus" property="cfgStatus" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="cfgSuccessId" jdbcType="BIGINT" property="cfgSuccessId" />
		<result column="cfgSuccessTime" jdbcType="TIMESTAMP" property="cfgSuccessTime" />

		<result column="ownEquipId" property="ownEquipId" />
		<result column="equipSwVer" property="equipSwVer"/>
		<result column="essEquipId" property="essEquipId" />
		<result column="essDno" property="essDno" />
		<result column="essName" property="essName" />
	</resultMap>

	<resultMap id="EQUIP_TINY_DTO_MAP" type="com.cdz360.iot.model.ess.dto.EssEquipTinyDto">
		<result column="dno" jdbcType="VARCHAR" property="dno"/>
		<result column="name" jdbcType="VARCHAR" property="name"/>
		<result column="status" jdbcType="INTEGER" property="status"
			typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
		<result column="equipType" jdbcType="INTEGER" property="equipType"
			typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
	</resultMap>

	<resultMap id="GTI_MONIT_VO_MAP" type="com.cdz360.iot.model.pv.vo.GtiMonitVo">
		<result column="dno" jdbcType="VARCHAR" property="dno"/>
		<result column="name" jdbcType="VARCHAR" property="name"/>
		<result column="serialNo" jdbcType="VARCHAR" property="serialNo"/>
		<result column="siteId" jdbcType="VARCHAR" property="siteId"/>
		<result column="siteName" jdbcType="VARCHAR" property="siteName"/>
		<result column="status" property="equipStatus" typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
		<result column="ratedPower" jdbcType="DECIMAL" property="ratedPower"/>
		<result column="stringCapacity" jdbcType="INTEGER" property="stringCapacity"/>
	</resultMap>



	<select id="getByGwnoAndDno"
			resultMap="RESULT_GTI_PO">	
		select * from t_gti where gwno = #{gwno} and dno = #{dno}

	</select>

	<select id="getByDno"
			resultMap="RESULT_GTI_PO">
		select * from t_gti where dno = #{dno}
	</select>

	<select id="getVoByDno" resultMap="RESULT_GTI_VO">
		select gti.*,
			site.name   as siteName,
			equip.swVer as equipSwVer
		from t_gti gti
		left join t_site site on site.dzid = gti.siteId
		left join t_ess_equip equip on equip.id = gti.essEquipId
		where gti.dno = #{dno}
	</select>

	<sql id="query_gti_condition">
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( gwno )">
			and gti.gwno = #{gwno}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( dno )">
			and gti.dno = #{dno}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
			and gti.siteId = #{siteId}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devNo )">
			and (gti.id = #{devNo} or CAST(gti.sid AS CHAR) = #{devNo})
		</if>
		<if test="status != null">
			and gti.status = #{status.code}
		</if>
		<if test="vendor != null">
			and gti.vendor = #{vendor}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( deviceModel )">
			and gti.deviceModel = #{deviceModel}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devName )">
			and gti.name like concat('%', #{devName}, '%')
		</if>
	</sql>

	<select id="findGtiList"
		parameterType="com.cdz360.iot.model.pv.param.ListGtiParam"
		resultMap="RESULT_GTI_VO">
		select
			gti.*,
			devCfg.name cfgSuccessName,
			site.name as siteName,
			gw.name AS gwName,
			equip.equipId as ownEquipId,
			equip.swVer as equipSwVer,
			ess.dno essDno,
			ess.name essName
		from t_gti gti
		left join t_gti_cfg cfgSuccess on cfgSuccess.cfgId = gti.cfgSuccessId
		left join t_dev_cfg devCfg on devCfg.id = cfgSuccess.cfgId
		LEFT JOIN t_gw_info gw ON gw.gwno = gti.gwno
		LEFT JOIN t_site site ON site.dzid = gti.siteId
		LEFT JOIN t_r_commercial trc ON site.commId = trc.id
		left join t_ess_equip equip on equip.id = gti.essEquipId
		left join t_ess ess on ess.dno = equip.essDno
		where gti.status != 99
		<include refid="query_gti_condition" />
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and trc.`idChain` like concat(#{commIdChain}, '%')
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( tempName )">
			and devCfg.name = #{tempName}
		</if>
		order by
			gti.id desc
		<choose>
			<when test="start != null and size != null">
				limit #{start},#{size}
			</when>
			<when test="size != null">
				limit #{size}
			</when>
		</choose>
	</select>

	<select id="count" resultType="java.lang.Long">
		select count(*)
		from t_gti gti
		left join t_gti_cfg cfgSuccess on cfgSuccess.cfgId = gti.cfgSuccessId
		left join t_dev_cfg devCfg on devCfg.id = cfgSuccess.cfgId
		LEFT JOIN t_site site ON site.dzid = gti.siteId
		LEFT JOIN t_r_commercial trc ON site.commId = trc.id
		where gti.status != 99
		<include refid="query_gti_condition" />
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and trc.`idChain` like concat(#{commIdChain}, '%')
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( tempName )">
			and devCfg.name = #{tempName}
		</if>
	</select>

	<select id="findGtiList2"
		parameterType="com.cdz360.iot.model.pv.param.ListGtiParam"
		resultMap="RESULT_GTI_VO">
		select
			gti.*,
			site.name as siteName,
			equip.swVer as equipSwVer
		from t_gti gti
		LEFT JOIN t_site site ON site.dzid = gti.siteId
		left join t_ess_equip equip on equip.id = gti.essEquipId
		where gti.status != 99
		<include refid="query_gti_condition" />
		order by
			gti.id desc
		<choose>
			<when test="start != null and size != null">
				limit #{start},#{size}
			</when>
			<when test="size != null">
				limit #{size}
			</when>
		</choose>
	</select>

	<select id="findGtiList2Count" resultType="java.lang.Long">
		select count(*)
		from t_gti gti
		LEFT JOIN t_site site ON site.dzid = gti.siteId
		left join t_ess_equip equip on equip.id = gti.essEquipId
		where gti.status != 99
		<include refid="query_gti_condition" />
	</select>

	<select id="getCountByCfgId" resultType="java.lang.Long">
		select count(*) from t_gti where cfgSuccessId = #{cfgId}
	</select>

	<select id="getByName"
		resultMap="RESULT_GTI_PO">
		select * from t_gti
		where status != 99 and
		<choose>
			<when test="null != includeDno and includeDno">
				<foreach item="gti" collection="gtiDtoList"
					open="(" separator="or" close=")">
					(name = #{gti.name} and dno != #{gti.dno})
				</foreach>
			</when>
			<otherwise>
				name in
				<foreach item="gti" collection="gtiDtoList"
					open="(" separator="," close=")">
					#{gti.name}
				</foreach>
			</otherwise>
		</choose>
		limit 1
	</select>

	<select id="getGtiStatusBi" resultType="com.cdz360.iot.model.pv.vo.GtiStatusBi">
		select
			gti.status ,
			count(*) as num
		from
			t_gti gti
		inner join t_site s on
			gti.siteId = s.dzId
		inner join t_r_commercial comm on
			s.commId = comm.id
		where
			gti.status in (1,2)
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and comm.idChain like concat(#{commIdChain},"%")
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
			and gti.siteId = #{siteId}
		</if>
		group by
			gti.status
	</select>

	<select id="getAbnormalGtiNum" resultType="java.lang.Long">
		select
			count(*) as num
		from
			t_gti gti
		inner join t_site s on
			gti.siteId = s.dzId
		inner join t_r_commercial comm on
			s.commId = comm.id
		where
			gti.status = 1
			and gti.alertStatus = 2
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and comm.idChain like concat(#{commIdChain},"%")
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
			and gti.siteId = #{siteId}
		</if>
	</select>
	<select id="getByEssEquipId" resultMap="RESULT_GTI_PO">
		select * from t_gti where essEquipId = #{essEquipId}
		limit 1
	</select>

	<select id="getByVendor" resultMap="RESULT_GTI_PO">
		select gti.* from t_gti gti
		inner join t_ess_equip equip on equip.dno = gti.serialNo
		where gti.vendor = #{vendor} and equip.equipType = #{equipType}
	</select>

	<select id="getDnoAndStatus" resultMap="EQUIP_TINY_DTO_MAP">
		select dno, status
		from t_gti
		where siteId = #{siteId}
	</select>

	<select id="findGtiMonitList" parameterType="com.cdz360.iot.model.pv.param.GtiMonitParam"
		resultMap="GTI_MONIT_VO_MAP">
		select
			g.dno,
			g.name,
			g.serialNo,
			g.siteId ,
			ANY_VALUE(s.name) as siteName,
			g.status as equipStatus,
			g.power as ratedPower,
			SUM(gs.capacity) as stringCapacity
		from
			t_gti g
		left join t_site s on
			g.siteId = s.dzId
		left join t_gti_string gs on
			g.dno = gs.gtiDno
		where
			g.siteId = #{siteId}
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( keyword )">
			and (g.dno like concat('%',#{keyword},'%') or g.name like concat('%',#{keyword},'%'))
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( status )">
			and g.status = #{status}
		</if>
		group by
			g.dno
		limit #{start}, #{size}
	</select>

	<select id="findGtiMonitListCount" resultType="java.lang.Long">
		select 
			count(distinct g.dno)
		from
			t_gti g
		left join t_site s on
			g.siteId = s.dzId
		left join t_gti_string gs on
			g.dno = gs.gtiDno
		where
			g.siteId = #{siteId}
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( keyword )">
			and (g.dno like concat('%',#{keyword},'%') or g.name like concat('%',#{keyword},'%'))
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( status )">
			and g.status = #{status}
		</if>
	</select>

</mapper>

