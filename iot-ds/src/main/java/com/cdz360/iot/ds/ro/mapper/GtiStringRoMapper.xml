<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.GtiStringRoMapper">

  <resultMap id="RESULT_GTI_STRING_PO" type="com.cdz360.iot.model.pv.po.GtiStringPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="gtiDno" jdbcType="VARCHAR" property="gtiDno"/>
    <result column="stringIdx" jdbcType="INTEGER" property="stringIdx"/>
    <result column="working" jdbcType="BOOLEAN" property="working"/>
    <result column="moduleVendor" jdbcType="VARCHAR" property="moduleVendor"/>
    <result column="moduleModel" jdbcType="VARCHAR" property="moduleModel"/>
    <result column="moduleType" property="moduleType"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="moduleNum" jdbcType="INTEGER" property="moduleNum"/>
    <result column="moduleMaxPower" jdbcType="DECIMAL" property="moduleMaxPower"/>
    <result column="capacity" jdbcType="DECIMAL" property="capacity"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById" resultMap="RESULT_GTI_STRING_PO">
    select *
    from t_gti_string
    where id = #{id}
  </select>

  <select id="getByGtiDno" resultMap="RESULT_GTI_STRING_PO">
    select * from t_gti_string
    where enable = true
    and gtiDno = #{gtiDno}
    <if test="working != null">
      and working = #{working}
    </if>
    order by stringIdx asc
  </select>

  <select id="getByGtiDnoCount" resultType="long">
    select count(*) from t_gti_string
    where enable = true
    and gtiDno = #{gtiDno}
    <if test="working != null">
      and working = #{working}
    </if>
  </select>

  <select id="getByGtiDnoPowerSum" resultType="com.cdz360.iot.model.pv.vo.GtiStringPowerVo">
    select
    gtiDno,
    sum(moduleMaxPower) modulePowerSum
    from t_gti_string
    where enable = true
    and working = true
    and gtiDno in
    <foreach collection="gtiDnoList" item="gtiDno" open="(" close=")" separator=",">
      #{gtiDno}
    </foreach>
    group by gtiDno
  </select>

  <select id="getGtiCapacityVo" resultType="com.cdz360.iot.model.pv.vo.GtiMonitVo">
    select
      gtiDno as dno,
      sum(capacity) as stringCapacity
    from
      t_gti_string
    where
        gtiDno in
    <foreach collection="gtiDnoList" item="gtiDno" open="(" close=")" separator=",">
      #{gtiDno}
    </foreach>
    group by
      gtiDno
  </select>

</mapper>
