package com.cdz360.iot.ess.utils.ts;

import com.cdz360.base.model.base.param.TimeRange;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.iot.ess.cfg.ts.TDengineSqlConstants;
import com.cdz360.iot.ess.model.vo.PowerData;
import com.cdz360.iot.model.taos.TsPvGtiPo;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

/**
 * 光伏逆变器数据访问类
 */
@Slf4j
@Repository
public class TsPvGtiUtil extends TDengineBaseUtil {

    public TsPvGtiUtil(JdbcTemplate jdbcTemplate) {
        super(jdbcTemplate);
    }

    /**
     * 创建子表
     */
    public void createTable(String deviceNo) {
        String sql = String.format(TDengineSqlConstants.CREATE_PV_GTI_TABLE, deviceNo);
        updateWithRetry(sql, deviceNo);
        log.info("创建光伏逆变器子表成功: pv_gti_{}", deviceNo);
    }

    /**
     * 单条插入数据
     */
    public void insertData(TsPvGtiPo data) {
        try {
            // 参数化查询，防止SQL注入
            String sql = TDengineSqlConstants.INSERT_PV_GTI_DATA;
            updateWithRetry(sql,
                data.getDno(),              // 子表名和TAG
                data.getDno(),              // TAG值
                Timestamp.valueOf(data.getTs()),
                Timestamp.valueOf(data.getSts()),
                data.getUab(), data.getUbc(), data.getUca(),
                data.getUa(), data.getUb(), data.getUc(),
                data.getIa(), data.getIb(), data.getIc(),
                data.getFa(), data.getFb(), data.getFc(),
                data.getP(), data.getQ(), data.getS(), data.getPf(), data.getF(),
                data.getIp(), data.getIso(),
                data.getTe(), data.getIt(), data.getRs(),
                data.getEpe(), data.getAh(), data.getTk()
            );
            log.debug("插入光伏逆变器数据成功: {}", data.getDno());
        } catch (Exception e) {
            log.error("插入光伏逆变器数据失败: {}, 错误: {}", data.getDno(), e.getMessage());
            throw e;
        }
    }

    /**
     * 批量插入数据 - 高性能
     */
    public void batchInsertData(List<TsPvGtiPo> dataList) {
        if (dataList.isEmpty()) {
            return;
        }

        String sql = TDengineSqlConstants.INSERT_PV_GTI_DATA;
        batchInsert(sql, dataList, (ps, data) -> {
            ps.setString(1, data.getDno());
            ps.setString(2, data.getDno());
            ps.setTimestamp(3, Timestamp.valueOf(data.getTs()));
            ps.setTimestamp(4, Timestamp.valueOf(data.getSts()));
            ps.setObject(5, data.getUab());
            ps.setObject(6, data.getUbc());
            ps.setObject(7, data.getUca());
            ps.setObject(8, data.getUa());
            ps.setObject(9, data.getUb());
            ps.setObject(10, data.getUc());
            ps.setObject(11, data.getIa());
            ps.setObject(12, data.getIb());
            ps.setObject(13, data.getIc());
            ps.setObject(14, data.getFa());
            ps.setObject(15, data.getFb());
            ps.setObject(16, data.getFc());
            ps.setObject(17, data.getP());
            ps.setObject(18, data.getQ());
            ps.setObject(19, data.getS());
            ps.setObject(20, data.getPf());
            ps.setObject(21, data.getF());
            ps.setObject(22, data.getIp());
            ps.setObject(23, data.getIso());
            ps.setObject(24, data.getTe());
            ps.setObject(25, data.getIt());
            ps.setObject(26, data.getRs());
            ps.setObject(27, data.getEpe());
            ps.setObject(28, data.getAh());
            ps.setObject(29, data.getTk());
        });
    }

    /**
     * 查询时间范围内的数据
     */
    public List<TsPvGtiPo> queryByTimeRange(String deviceNo, LocalDateTime start,
        LocalDateTime end) {
        String sql = TDengineSqlConstants.QUERY_PV_GTI_BY_TIME_RANGE;
        return jdbcTemplate.query(sql,
            new Object[]{deviceNo, Timestamp.valueOf(start), Timestamp.valueOf(end)},
            new BeanPropertyRowMapper<>(TsPvGtiPo.class));
    }

    /**
     * 查询最新数据
     */
    public TsPvGtiPo queryLatest(String gtiDno) {
        String sql = TDengineSqlConstants.QUERY_PV_GTI_LATEST;
        List<TsPvGtiPo> results = jdbcTemplate.query(sql,
            new Object[]{gtiDno},
            new BeanPropertyRowMapper<>(TsPvGtiPo.class));
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * 批量查询多个GTI最新数据
     */
    public Map<String, TsPvGtiPo> queryLatestBatch(List<String> gtiDnoList) {
        if (CollectionUtils.isEmpty(gtiDnoList)) {
            return Collections.emptyMap();
        }

        try {
            // 构建IN子句的占位符
            String placeholders = gtiDnoList.stream()
                .map(dno -> "'" + dno + "'")
                .collect(Collectors.joining(","));

            String sql = String.format(TDengineSqlConstants.QUERY_PV_GTI_LATEST_MULTI,
                placeholders);

            List<TsPvGtiPo> results = jdbcTemplate.query(sql,
                new BeanPropertyRowMapper<>(TsPvGtiPo.class));

            return results.stream()
                .collect(Collectors.toMap(TsPvGtiPo::getDno, Function.identity()));

        } catch (Exception e) {
            log.error("批量查询GTI最新数据失败: {}, 错误: {}", gtiDnoList, e.getMessage());
            throw e;
        }
    }

    /**
     * 查询多个GTI时间范围内的有功功率数据（批量优化查询）
     */
    public List<PowerData> queryPowerByTimeRange(List<String> dnoList, TimeRange timeRange) {
        if (dnoList == null || dnoList.isEmpty()) {
            return List.of();
        }

        LocalDateTime start = timeRange.getFrom();
        LocalDateTime end = timeRange.getTo();

        // 使用超级表查询，替代UNION ALL方式，性能更优
        String placeholders = dnoList.stream()
            .map(code -> "?")
            .collect(Collectors.joining(","));

        String sql = String.format(TDengineSqlConstants.QUERY_PV_GTI_POWER_MULTI_BY_TIME_RANGE,
            placeholders);

        return jdbcTemplate.query(sql,
            ps -> {
                int paramIndex = 1;
                // 设置dno参数
                for (String dno : dnoList) {
                    ps.setString(paramIndex++, dno);
                }
                // 设置时间范围参数
                ps.setTimestamp(paramIndex++, Timestamp.valueOf(start));
                ps.setTimestamp(paramIndex, Timestamp.valueOf(end));
            },
            (rs, rowNum) -> new PowerData()
                .setTs(rs.getTimestamp("ts").toLocalDateTime())
                .setDeviceCode(rs.getString("dno"))
                .setP(rs.getDouble("p")));
    }

}
