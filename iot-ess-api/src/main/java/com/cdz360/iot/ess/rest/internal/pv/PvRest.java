package com.cdz360.iot.ess.rest.internal.pv;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.biz.PvService;
import com.cdz360.iot.ess.model.param.GtiEditParam;
import com.cdz360.iot.ess.timeseries.service.TsPvGtiService;
import com.cdz360.iot.model.pv.param.GtiMonitParam;
import com.cdz360.iot.model.pv.param.ListGtiParam;
import com.cdz360.iot.model.pv.vo.GtiMonitVo;
import com.cdz360.iot.model.pv.vo.GtiVo;
import com.cdz360.iot.model.taos.TsPvGtiPo;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/iot/pv")
public class PvRest {

    @Autowired
    private PvService pvService;

    @Autowired
    private TsPvGtiService tsPvGtiService;

    @PostMapping("/addGti")
    @Operation(summary = "新增光伏逆变器", description = "新增一个光伏逆变器设备")
    public BaseResponse addGti(@RequestBody GtiEditParam param) {
        return pvService.addGti(param);
    }

    @PostMapping("/gti/update")
    @Operation(summary = "修改光伏逆变器", description = "根据设备编号修改光伏逆变器信息")
    public BaseResponse updateGti(@RequestBody GtiEditParam param) {
        return pvService.updateGti(param);
    }

    @GetMapping("/gti/dno/{dno}")
    @Operation(summary = "根据设备编号查询光伏逆变器", description = "根据设备编号查询逆变器详细信息")
    public Mono<ObjectResponse<GtiVo>> getGtiByDno(@PathVariable @NotBlank String dno) {
        return pvService.getGtiByDno(dno).map(RestUtils::buildObjectResponse)
            .switchIfEmpty(Mono.just(RestUtils.buildObjectResponse(null)));
    }

    @PostMapping("/gti/list")
    @Operation(summary = "批量查询光伏逆变器", description = "根据条件分页查询光伏逆变器列表")
    public Mono<ListResponse<GtiVo>> listGti(@RequestBody ListGtiParam param) {
        return pvService.listGti(param);
    }

    @PostMapping("/gti/delete/{dno}")
    @Operation(summary = "删除光伏逆变器", description = "根据设备编号删除光伏逆变器")
    public BaseResponse deleteGti(@PathVariable String dno) {
        return pvService.deleteGti(dno);
    }

    @PostMapping("/gti/monit/list")
    @Operation(summary = "查询逆变器监控数据")
    public Mono<ListResponse<GtiMonitVo>> getGtiMonitVoList(@RequestBody GtiMonitParam param) {
        return pvService.getGtiMonitVoList(param);
    }

    @PostMapping("/gti/batchSave")
    @Operation(description = "批量保存逆变器时序数据")
    public BaseResponse batchSavePvGtiData(@RequestBody List<TsPvGtiPo> tsPvGtiPoList) {
        return tsPvGtiService.batchSaveData(tsPvGtiPoList);
    }

}
