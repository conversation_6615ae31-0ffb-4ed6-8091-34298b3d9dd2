package com.cdz360.iot.ess.timeseries.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.utils.ts.TsPvGtiUtil;
import com.cdz360.iot.model.taos.TsPvGtiPo;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 光伏逆变器服务类
 */
@Slf4j
@Service
public class TsPvGtiService {

    @Autowired
    private TsPvGtiUtil tsPvGtiUtil;

    /**
     * 创建子表
     */
    public void createTable(String deviceNo) {
        tsPvGtiUtil.createTable(deviceNo);
    }

    /**
     * 保存单条数据
     */
    public void saveData(TsPvGtiPo data) {
        // 参数验证
        checkAndSetData(data);

        // 保存数据
        tsPvGtiUtil.insertData(data);

        log.info("保存光伏逆变器数据成功: {}", data.getDno());
    }

    /**
     * 批量保存数据 - 高性能
     */
    @Transactional
    public BaseResponse batchSaveData(List<TsPvGtiPo> dataList) {
        try {
            // 参数验证
            dataList.forEach(this::checkAndSetData);

            // 批量保存数据
            tsPvGtiUtil.batchInsertData(dataList);

            log.info("批量保存光伏逆变器数据成功，数量: {}", dataList.size());
            return RestUtils.success();
        } catch (Exception e) {
            log.error("批量保存光伏逆变器数据失败: {}", e.getMessage(), e);
            return RestUtils.serviceFail("批量保存数据失败: " + e.getMessage());
        }
    }

    /**
     * 数据验证
     */
    private void checkAndSetData(TsPvGtiPo data) {
        if (data == null) {
            throw new DcArgumentException("数据不能为空");
        }
        if (data.getDno() == null || data.getDno().trim().isEmpty()) {
            throw new DcArgumentException("设备编号不能为空");
        }
        if (data.getTs() == null) {
            throw new DcArgumentException("采集时间不能为空");
        }
        if (data.getSts() == null) {
            data.setSts(LocalDateTime.now()); // 自动设置服务器时间
        }
    }
}
