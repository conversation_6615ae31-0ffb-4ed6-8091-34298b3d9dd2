package com.cdz360.iot.ess.utils.ts;

import com.cdz360.iot.model.taos.TsPvGtiPo;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import org.springframework.jdbc.core.RowMapper;

/**
 * TDengine LAST函数结果的专用RowMapper
 * 处理LAST(*)返回的last_xxx列名映射问题
 */
public class TsPvGtiLastRowMapper implements RowMapper<TsPvGtiPo> {

    @Override
    public TsPvGtiPo mapRow(ResultSet rs, int rowNum) throws SQLException {
        TsPvGtiPo po = new TsPvGtiPo();
        
        // TAG字段
        po.setDno(rs.getString("dno"));
        
        // 时间字段 - 处理LAST函数返回的列名
        po.setTs(getLocalDateTime(rs, "ts", "last_ts"));
        po.setSts(getLocalDateTime(rs, "sts", "last_sts"));
        
        // 交流侧电压
        po.setUab(getDouble(rs, "uab", "last_uab"));
        po.setUbc(getDouble(rs, "ubc", "last_ubc"));
        po.setUca(getDouble(rs, "uca", "last_uca"));
        po.setUa(getDouble(rs, "ua", "last_ua"));
        po.setUb(getDouble(rs, "ub", "last_ub"));
        po.setUc(getDouble(rs, "uc", "last_uc"));
        
        // 交流侧电流
        po.setIa(getDouble(rs, "ia", "last_ia"));
        po.setIb(getDouble(rs, "ib", "last_ib"));
        po.setIc(getDouble(rs, "ic", "last_ic"));
        
        // 频率
        po.setFa(getDouble(rs, "fa", "last_fa"));
        po.setFb(getDouble(rs, "fb", "last_fb"));
        po.setFc(getDouble(rs, "fc", "last_fc"));
        
        // 功率与电能质量
        po.setP(getDouble(rs, "p", "last_p"));
        po.setQ(getDouble(rs, "q", "last_q"));
        po.setS(getDouble(rs, "s", "last_s"));
        po.setPf(getDouble(rs, "pf", "last_pf"));
        po.setF(getDouble(rs, "f", "last_f"));
        
        // 直流侧
        po.setIp(getDouble(rs, "ip", "last_ip"));
        po.setIso(getDouble(rs, "iso", "last_iso"));
        
        // 整体性能与状态
        po.setTe(getDouble(rs, "te", "last_te"));
        po.setIt(getDouble(rs, "it", "last_it"));
        po.setRs(getInteger(rs, "rs", "last_rs"));
        
        // 发电量统计
        po.setEpe(getDouble(rs, "epe", "last_epe"));
        po.setAh(getDouble(rs, "ah", "last_ah"));
        po.setTk(getDouble(rs, "tk", "last_tk"));
        
        return po;
    }
    
    /**
     * 获取Double值，优先使用别名，如果不存在则使用LAST函数的原始列名
     */
    private Double getDouble(ResultSet rs, String alias, String lastColumnName) throws SQLException {
        try {
            // 优先尝试别名
            return rs.getDouble(alias);
        } catch (SQLException e) {
            // 如果别名不存在，尝试LAST函数的原始列名
            try {
                return rs.getDouble(lastColumnName);
            } catch (SQLException ex) {
                return null;
            }
        }
    }
    
    /**
     * 获取Integer值，优先使用别名，如果不存在则使用LAST函数的原始列名
     */
    private Integer getInteger(ResultSet rs, String alias, String lastColumnName) throws SQLException {
        try {
            return rs.getInt(alias);
        } catch (SQLException e) {
            try {
                return rs.getInt(lastColumnName);
            } catch (SQLException ex) {
                return null;
            }
        }
    }
    
    /**
     * 获取LocalDateTime值，优先使用别名，如果不存在则使用LAST函数的原始列名
     */
    private LocalDateTime getLocalDateTime(ResultSet rs, String alias, String lastColumnName) throws SQLException {
        try {
            var timestamp = rs.getTimestamp(alias);
            return timestamp != null ? timestamp.toLocalDateTime() : null;
        } catch (SQLException e) {
            try {
                var timestamp = rs.getTimestamp(lastColumnName);
                return timestamp != null ? timestamp.toLocalDateTime() : null;
            } catch (SQLException ex) {
                return null;
            }
        }
    }
}
