package com.cdz360.iot.ess.cfg.ts;

/**
 * TDengine SQL常量定义
 */
public class TDengineSqlConstants {

    // ============ 光伏逆变器相关SQL ============

    /**
     * 创建光伏逆变器子表
     */
    public static final String CREATE_PV_GTI_TABLE =
        "CREATE TABLE IF NOT EXISTS pv_gti_%s USING pv_gti_super TAGS (?)";

    /**
     * 插入光伏逆变器数据 - 参数化查询
     * 参数顺序：表名, TAG, ts, sts, uab, ubc, uca, ua, ub, uc, ia, ib, ic, fa, fb, fc, p, q, s, pf, f, ip, iso, te, it, rs, epe, ah, tk
     * 总共27个字段值参数
     */
    public static final String INSERT_PV_GTI_DATA =
        "INSERT INTO pv_gti_? USING pv_gti_super TAGS (?) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    /**
     * 查询光伏逆变器数据 - 时间范围
     */
    public static final String QUERY_PV_GTI_BY_TIME_RANGE =
        "SELECT * FROM pv_gti_? WHERE ts >= ? AND ts < ? ORDER BY ts";

    /**
     * 查询光伏逆变器最新数据
     */
    public static final String QUERY_PV_GTI_LATEST =
        "SELECT LAST(*) FROM pv_gti_? WHERE dno = ?";

    /**
     * 查询多个GTI最新数据 - 超级表查询（高性能）
     */
    public static final String QUERY_PV_GTI_LATEST_MULTI =
        "SELECT dno, LAST(*) FROM pv_gti_super WHERE dno IN (%s) GROUP BY dno";

    /**
     * 查询多个GTI有功功率数据 - 超级表查询（高性能）
     */
    public static final String QUERY_PV_GTI_POWER_MULTI_BY_TIME_RANGE =
        "SELECT ts, dno, p FROM pv_gti_super WHERE dno IN (%s) AND ts >= ? AND ts < ? ORDER BY ts";

    // ============ 光伏组串相关SQL ============

    /**
     * 创建光伏组串子表
     * TAG字段：dno（逆变器编号）, idx（组串ID）
     */
    public static final String CREATE_PV_STRING_TABLE =
        "CREATE TABLE IF NOT EXISTS pv_string_%s_%s USING pv_string_super TAGS (?, ?)";

    /**
     * 插入光伏组串数据 - 参数化查询
     * 参数顺序：表名, dno(TAG), idx(TAG), ts, sts, u, i
     * 总共4个数据字段参数（ts, sts, u, i）
     */
    public static final String INSERT_PV_STRING_DATA =
        "INSERT INTO pv_string_?_? USING pv_string_super TAGS (?, ?) VALUES (?, ?, ?, ?)";

    /**
     * 查询光伏组串数据 - 时间范围
     */
    public static final String QUERY_PV_STRING_BY_TIME_RANGE =
        "SELECT * FROM pv_string_?_? WHERE ts >= ? AND ts < ? ORDER BY ts";

    /**
     * 查询组串最新数据
     */
    public static final String QUERY_PV_STRING_LATEST =
        "SELECT LAST(*) FROM pv_string_?_? WHERE dno = ? AND idx = ?";

    /**
     * 查询多个组串最新数据 - 超级表查询（高性能）
     */
    public static final String QUERY_PV_STRING_LATEST_MULTI =
        "SELECT dno, idx, LAST(*) FROM pv_string_super WHERE dno IN (%s) GROUP BY dno, idx";

    // ============ 电气设备监控相关SQL ============

    /**
     * 创建电气设备监控子表
     */
    public static final String CREATE_ELEC_EQUIP_TABLE =
        "CREATE TABLE IF NOT EXISTS elec_equip_monitor_%s USING elec_equip_monitor_super TAGS (?)";

    /**
     * 插入电气设备监控数据 - 参数化查询
     */
    public static final String INSERT_ELEC_EQUIP_DATA =
        "INSERT INTO elec_equip_monitor_? USING elec_equip_monitor_super TAGS (?) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    /**
     * 查询电气设备监控数据 - 时间范围
     */
    public static final String QUERY_ELEC_EQUIP_BY_TIME_RANGE =
        "SELECT * FROM elec_equip_monitor_? WHERE ts >= ? AND ts < ? ORDER BY ts";

    /**
     * 查询电气设备最新数据
     */
    public static final String QUERY_ELEC_EQUIP_LATEST =
        "SELECT LAST(*) FROM elec_equip_monitor_? WHERE equipCode = ?";

    /**
     * 查询电气设备有功功率数据 - 时间范围（优化查询）
     */
    public static final String QUERY_ELEC_EQUIP_POWER_BY_TIME_RANGE =
        "SELECT ts, equipCode, p FROM elec_equip_monitor_? WHERE ts >= ? AND ts < ? ORDER BY ts";

    /**
     * 查询多个电气设备有功功率数据 - 超级表查询（高性能）
     */
    public static final String QUERY_ELEC_EQUIP_POWER_MULTI_BY_TIME_RANGE =
        "SELECT ts, equipCode, p FROM elec_equip_monitor_super WHERE equipCode IN (%s) AND ts >= ? AND ts < ? ORDER BY ts";

}
