package com.cdz360.iot.ess.biz;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.utils.DnoGenerator;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.GtiStringRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiRoDs;
import com.cdz360.iot.ds.rw.EssEquipRwDs;
import com.cdz360.iot.ds.rw.GtiRwDs;
import com.cdz360.iot.ds.rw.GtiStringRwDs;
import com.cdz360.iot.ess.model.param.GtiEditParam;
import com.cdz360.iot.ess.utils.ts.TsPvGtiUtil;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.pv.param.GtiMonitParam;
import com.cdz360.iot.model.pv.param.ListGtiParam;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.pv.type.GtiVendor;
import com.cdz360.iot.model.pv.vo.GtiMonitVo;
import com.cdz360.iot.model.pv.vo.GtiStringPowerVo;
import com.cdz360.iot.model.pv.vo.GtiVo;
import com.cdz360.iot.model.taos.TsPvGtiPo;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PvService {

    @Autowired
    private GtiRwDs gtiRwDs;

    @Autowired
    private GtiRoDs gtiRoDs;

    @Autowired
    private DnoGenerator dnoGenerator;

    @Autowired
    private EssEquipRwDs essEquipRwDs;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @Autowired
    private GtiStringRoDs gtiStringRoDs;

    @Autowired
    private GtiStringRwDs gtiStringRwDs;

    @Autowired
    private TsPvGtiUtil tsPvGtiUtil;

    /**
     * 新增光伏逆变器
     *
     * @param param 逆变器信息
     * @return 新增结果
     */
    @Transactional
    public BaseResponse addGti(GtiEditParam param) {
        EssEquipType equipType = EssEquipType.PV_INV;

        GtiPo gtiPo = this.checkAndSetGtiPo(param);
        if (StringUtils.isBlank(gtiPo.getGwno())) {
            gtiPo.setGwno("");
        }
        gtiPo.setDno(dnoGenerator.genDno(equipType));
        gtiPo.setCreateTime(new Date());

        String essDno = gtiPo.getSiteId(); // 特殊处理，填入SiteId
        // 先查找该场站下该设备类型的最大equipId
        int maxEquipId = essEquipRoDs.getMaxEquipIdByEquipType(essDno, equipType);
        long newEquipId = maxEquipId + 1;

        // 插入 t_ess_equip，使用递增的equipId
        EssEquipPo essEquipPo = new EssEquipPo();
        essEquipPo.setEssDno(essDno)
            .setDno(gtiPo.getDno())
            .setName(gtiPo.getName())
            .setEquipId(newEquipId)
            .setEquipType(equipType)
            .setEquipTypeId(equipType.getCode())
            .setEquipNameCn(equipType.getDesc())
            .setEquipNameEn(equipType.getDesc())
            .setEnable(true)
            .setStatus(EquipStatus.NORMAL)
            .setCreateTime(new Date());
        if (StringUtils.isNotBlank(param.getDeviceModel())) {
            essEquipPo.setEquipModel(param.getDeviceModel());
        }
        if (StringUtils.isNotBlank(param.getSwVer())) {
            essEquipPo.setSwVer(param.getSwVer());
        }
        boolean essInsertResult = essEquipRwDs.insertEssEquip(essEquipPo);
        if (!essInsertResult) {
            throw new DcServiceException("保存失败");
        }

        // 将id填入essEquipId，再插入 t_gti
        gtiPo.setEssEquipId(essEquipPo.getId());
        boolean gtiInsertResult = gtiRwDs.upsetGti(gtiPo);

        return gtiInsertResult ? RestUtils.success()
            : RestUtils.serviceFail("保存失败");
    }

    /**
     * 修改光伏逆变器
     *
     * @param param 逆变器信息
     * @return 修改结果
     */
    @Transactional
    public BaseResponse updateGti(GtiEditParam param) {
        IotAssert.isNotBlank(param.getDno(), "设备编号不能为空");

        GtiPo gtiPo = this.checkAndSetGtiPo(param);
        gtiPo.setUpdateTime(new Date());

        if (StringUtils.isNotBlank(param.getDeviceModel()) || StringUtils.isNotEmpty(
            param.getSwVer())) {

            EssEquipPo essEquipPo = new EssEquipPo();
            essEquipPo.setDno(gtiPo.getDno());
            essEquipPo.setName(gtiPo.getName());
            essEquipPo.setEssDno(gtiPo.getSiteId()); // 特殊处理，填入SiteId
            essEquipPo.setUpdateTime(new Date());
            if (StringUtils.isNotBlank(gtiPo.getDeviceModel())) {
                essEquipPo.setEquipModel(gtiPo.getDeviceModel());
            }
            if (StringUtils.isNotBlank(param.getSwVer())) {
                essEquipPo.setSwVer(param.getSwVer());
            }
            boolean essUpdateResult = essEquipRwDs.updateEssEquip(essEquipPo);
            if (!essUpdateResult) {
                throw new DcServiceException("保存失败");
            }
        }

        // 更新t_gti
        boolean updateResult = gtiRwDs.updateGtiByDno(gtiPo);
        return updateResult ? RestUtils.success()
            : RestUtils.serviceFail("保存失败");
    }

    /**
     * 根据设备编号查询光伏逆变器
     *
     * @param dno 设备编号
     * @return 逆变器信息
     */
    public Mono<GtiVo> getGtiByDno(String dno) {
        return Mono.just(dno)
            .doOnNext(this::validateDno)
            .flatMap(dnoValue -> Mono.justOrEmpty(gtiRoDs.getVoByDno(dnoValue)));
    }

    /**
     * 批量查询光伏逆变器
     *
     * @param param 查询参数
     * @return 逆变器列表
     */
    public Mono<ListResponse<GtiVo>> listGti(ListGtiParam param) {
        return Mono.just(param)
            .filter(Objects::nonNull)
            .switchIfEmpty(Mono.error(new DcArgumentException("查询参数不能为空")))
            .map(p -> {
                List<GtiVo> list = gtiRoDs.findGtiList2(p);
                Long total = gtiRoDs.findGtiList2Count(p);

                if (CollectionUtils.isNotEmpty(list)) {
                    List<String> gtiDnoList = list.stream().map(GtiVo::getDno)
                        .collect(Collectors.toList());
                    List<GtiStringPowerVo> powerSumList = gtiStringRoDs.getByGtiDnoPowerSum(
                        gtiDnoList);
                    if (CollectionUtils.isNotEmpty(powerSumList)) {
                        Map<String, BigDecimal> collect = powerSumList.stream().collect(
                            Collectors.toMap(GtiStringPowerVo::getGtiDno,
                                GtiStringPowerVo::getModulePowerSum));
                        list.forEach(gtiVo -> gtiVo.setModulePowerSum(
                            collect.getOrDefault(gtiVo.getDno(), null)));
                    }
                }

                return new ListResponse<>(list, total);
            });
    }

    /**
     * 删除光伏逆变器
     *
     * @param dno 设备编号
     * @return 删除结果
     */
    @Transactional
    public BaseResponse deleteGti(String dno) {
        IotAssert.isNotBlank(dno, "设备编号不能为空");

        // 先检查设备是否存在
        GtiPo existingGti = gtiRoDs.getByDno(dno);
        if (existingGti == null) {
            throw new DcArgumentException("设备不存在: " + dno);
        }

        GtiPo gtiPo = new GtiPo();
        gtiPo.setDno(dno);
        gtiPo.setUpdateTime(new Date());
        // 这里可以根据业务需求设置状态为删除或下线
        gtiPo.setStatus(EquipStatus.OFF);
        boolean updateResult = gtiRwDs.updateGtiByDno(gtiPo);
        if (!updateResult) {
            throw new DcServiceException("保存失败");
        }

        // 更新t_ess_equip表状态
        EssEquipPo essEquipPo = new EssEquipPo();
        essEquipPo.setDno(dno);
        essEquipPo.setEssDno(existingGti.getSiteId()); // 特殊处理，填入SiteId
        essEquipPo.setStatus(EquipStatus.OFF);
        essEquipPo.setUpdateTime(new Date());
        boolean essUpdateResult = essEquipRwDs.updateEssEquip(essEquipPo);
        if (!essUpdateResult) {
            throw new DcServiceException("保存失败");
        }

        // 使逆变器对应组串也失效，不校验结果
        gtiStringRwDs.disableAllByGtiDno(dno);

        return RestUtils.success();
    }

    /**
     * 验证逆变器数据
     *
     * @param param 逆变器信息
     */
    private GtiPo checkAndSetGtiPo(GtiEditParam param) {
        IotAssert.isNotNull(param, "逆变器信息不能为空");
        IotAssert.isNotBlank(param.getName(), "设备名称不能为空");
        IotAssert.isNotBlank(param.getSiteId(), "场站ID不能为空");
        IotAssert.isNotNull(param.getStatus(), "设备状态不能为空");

        GtiPo gtiPo = new GtiPo();
        gtiPo.setDno(param.getDno())
            .setName(param.getName())
            .setSerialNo(param.getSerialNo())
            .setDeviceModel(param.getDeviceModel())
            .setSiteId(param.getSiteId())
            .setStatus(param.getStatus())
            .setMpptVoltageMin(param.getMpptVoltageMin())
            .setMpptVoltageMax(param.getMpptVoltageMax())
            .setPower(param.getPower())
            .setOutputVoltage(param.getOutputVoltage())
            .setOutputCurrent(param.getOutputCurrent())
            .setApparentPower(param.getApparentPower())
            .setCom(param.getCom());

        if (param.getVendor() != null) {
            gtiPo.setVendor(param.getVendor());
        } else {
            gtiPo.setVendor(GtiVendor.UNKNOWN);
        }
        return gtiPo;
    }

    /**
     * 验证设备编号
     *
     * @param dno 设备编号
     */
    private void validateDno(String dno) {
        IotAssert.isNotBlank(dno, "设备编号不能为空");
    }

    public Mono<ListResponse<GtiMonitVo>> getGtiMonitVoList(GtiMonitParam param) {
        IotAssert.isTrue(param.getStart() != null && param.getSize() != null, "分页参数不能为空");
        IotAssert.isNotNull(param.getSiteId(), "场站ID不能为空");

        // 查询MySQL数据
        List<GtiMonitVo> list = gtiRoDs.findGtiMonitList(param);
        Long count = gtiRoDs.findGtiMonitListCount(param);

        if (CollectionUtils.isNotEmpty(list)) {
            List<String> dnoList = list.stream().map(GtiMonitVo::getDno).toList();
            // 获取taos实时信息
            Map<String, TsPvGtiPo> latestDataMap = tsPvGtiUtil.queryLatestBatch(dnoList);

            List<GtiMonitVo> gtiCapacityVoList = gtiStringRoDs.getGtiCapacityVo(dnoList);
            Map<String, Integer> gtiCapacityVoMap = gtiCapacityVoList.stream()
                .collect(Collectors.toMap(GtiMonitVo::getDno, GtiMonitVo::getStringCapacity));

            list.forEach(vo -> {
                Optional.ofNullable(latestDataMap.get(vo.getDno())).ifPresent(tsPvGtiPo -> {
                    Optional.ofNullable(tsPvGtiPo.getP())
                        .ifPresent(x -> vo.setPower(BigDecimal.valueOf(x)));
                    Optional.ofNullable(tsPvGtiPo.getTk()).ifPresent(x -> {
                        BigDecimal kwh = BigDecimal.valueOf(x);
                        vo.setKwh(kwh);
                        if (DecimalUtils.gtZero(kwh) && vo.getRatedPower() != null) {
                            vo.setEquivalentTime(
                                kwh.divide(vo.getRatedPower(), 4, RoundingMode.HALF_UP));
                        }
                    });
                });

                vo.setStringCapacity(gtiCapacityVoMap.getOrDefault(vo.getDno(), null));
            });
        }

        return Mono.just(RestUtils.buildListResponse(list, count));
    }

}
