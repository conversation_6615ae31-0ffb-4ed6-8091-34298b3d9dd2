package com.cdz360.iot.pv.biz;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.DevCfgRoDs;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.SrsRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiCfgRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiGridDispatchCfgRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiRoDs;
import com.cdz360.iot.ds.rw.GtiDailyRwDs;
import com.cdz360.iot.ds.rw.GtiRwDs;
import com.cdz360.iot.model.ess.po.DevCfgPo;
import com.cdz360.iot.model.ess.type.CfgType;
import com.cdz360.iot.model.pv.dto.GtiGridDispatchCfgDto;
import com.cdz360.iot.model.pv.dto.PvCfgDto;
import com.cdz360.iot.model.pv.po.GtiCfgPo;
import com.cdz360.iot.model.pv.po.GtiDailyPo;
import com.cdz360.iot.model.pv.po.GtiGridDispatchCfgPo;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.pv.type.GtiVendor;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.srs.po.SrsPo;
import com.cdz360.iot.model.taos.TsPvGtiPo;
import com.cdz360.iot.model.taos.TsPvStringPo;
import com.cdz360.iot.pv.biz.south.GtiMqService;
import com.cdz360.iot.pv.biz.south.HwCloudClientService;
import com.cdz360.iot.pv.feign.EssFeignClient;
import com.cdz360.iot.pv.model.dto.HwCloudDeviceKpiDayRequest;
import com.cdz360.iot.pv.model.dto.HwCloudDeviceKpiDayResponse;
import com.cdz360.iot.pv.model.dto.HwCloudDeviceRealKpiRequest;
import com.cdz360.iot.pv.model.dto.HwCloudDeviceRealKpiResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;

@Slf4j
@Service
public class GtiBizService {

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private GtiRoDs gtiRoDs;

    @Autowired
    private SrsRoDs srsRoDs;

    @Autowired
    private GtiRwDs gtiRwDs;

    @Autowired
    private DevCfgRoDs devCfgRoDs;

    @Autowired
    private GtiCfgRoDs gtiCfgRoDs;

    @Autowired
    private GtiGridDispatchCfgRoDs gtiGridDispatchCfgRoDs;

    @Autowired
    private GtiMqService gtiMqService;

    @Autowired
    private GtiDailyRwDs gtiDailyRwDs;

    @Autowired
    private HwCloudClientService hwCloudClientService;

    @Autowired
    private EssFeignClient essFeignClient;

    /**
     * 下发获取逆变器信息的指令
     *
     * @param siteIdIn
     * @param gtiNos
     * @return
     */
    public Mono<Boolean> getGtiInfo(String siteIdIn, List<String> gtiNos) {
        return Mono.just(siteIdIn)
            .doOnNext(siteId -> {
                GwInfoPo gwInfo = this.getGwInfo(siteId);
                this.gtiMqService.getGtiInfo(gwInfo, gtiNos);
            })
            .map(siteId -> Boolean.TRUE);
    }

    /**
     * 下发获取逆变器信息的指令
     *
     * @param dno 逆变器编号
     * @return
     */
    public Mono<Boolean> getGtiInfo(String dno) {
        if (StringUtils.isBlank(dno)) {
            throw new DcArgumentException("逆变器编号无效");
        }

        return Mono.just(dno)
            .doOnNext(d -> {
                GtiPo gti = gtiRoDs.getByDno(d);
                if (null == gti) {
                    throw new DcArgumentException("逆变器不存在或已失效");
                }

                if (StringUtils.isBlank(gti.getGwno())) {
                    throw new DcArgumentException("逆变器控制器编号无效");
                }

                GwInfoDto gw = gwSiteRefRoDs.getGwInfoByGwno(gti.getGwno(), true);
                if (null == gw) {
                    throw new DcArgumentException("逆变器所属控制器不存在或已失效");
                }

                this.gtiMqService.getGtiInfo(gw, List.of(dno));
            })
            .map(siteId -> Boolean.TRUE);
    }

    /**
     * 修改逆变器配置信息
     *
     * @return
     */
    public Mono<Boolean> modifyGtiCfg(String gwnoIn, String dnoIn, Long cfgId) {
        if (StringUtils.isBlank(gwnoIn)) {
            throw new DcArgumentException("控制器编号无效");
        }
        if (StringUtils.isBlank(dnoIn)) {
            throw new DcArgumentException("逆变器编号无效");
        }
        IotAssert.isNotNull(cfgId, "配置模板ID无效");

        return Mono.just(gwnoIn)
            .doOnNext(gwno -> {
                GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfoByGwno(gwno, true);
                if (null == gwInfo) {
                    throw new DcArgumentException("控制器编号无效");
                }

                GtiPo gti = gtiRoDs.getByGwnoAndDno(gwno, dnoIn);
                if (null == gti) {
                    throw new DcArgumentException("控制器不存在指定的逆变器");
                }

                DevCfgPo devCfgPo = devCfgRoDs.getById(cfgId);
                if (null == devCfgPo) {
                    throw new DcArgumentException("配置ID无效");
                }

                SrsPo srsPo = srsRoDs.getOneByGwno(gwno);

                GtiPo update = new GtiPo();
                update.setDno(gti.getDno())
                    .setCfgId(cfgId)
                    .setCfgStatus(EquipCfgStatus.SEND_2_GW);
                gtiRwDs.updateGtiByDno(update);

                PvCfgDto dto = new PvCfgDto();
                dto.setId(gti.getSid())
                    .setSiteId(gti.getSiteId())
                    .setDeviceNo(gti.getDno());
                if (CfgType.GOOD_WE_GTI.equals(devCfgPo.getType())) {
                    GtiCfgPo cfg = gtiCfgRoDs.getById(cfgId);
                    dto.setSamplingTime(cfg.getSamplingTime())
                        .setBootVoltage(cfg.getBootVoltage())
                        .setMaxVoltage(cfg.getMaxVoltage())
                        .setMinVoltage(cfg.getMinVoltage())
                        .setMaxFrequency(cfg.getMaxFrequency())
                        .setMinFrequency(cfg.getMinFrequency());
                } else if (CfgType.HUAWEI_GTI.equals(devCfgPo.getType())) {
                    GtiGridDispatchCfgPo cfg = gtiGridDispatchCfgRoDs.getById(cfgId);
                    dto.setSamplingTime(cfg.getSamplingTime());

                    GtiGridDispatchCfgDto cfgDto = new GtiGridDispatchCfgDto();
                    BeanUtils.copyProperties(cfg, cfgDto);
                    dto.setGridDispatchCfg(cfgDto);
                } else {
                    throw new DcServiceException("不支持的模板类型");
                }

                this.gtiMqService.modifyGtiCfg(gwInfo,
                    devCfgPo.getVer(),
                    srsPo,
                    false,
                    List.of(dto));
            })
            .map(siteId -> Boolean.TRUE);
    }

    private GwInfoPo getGwInfo(String siteId) {
        GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfo(siteId);
        if (gwInfo == null) {
            log.error("can't find gwno for siteId = {}", siteId);
            throw new DcArgumentException("请配置场站对应的网关");
        }
        return gwInfo;
    }

    public Mono<Boolean> uploadGtiDataFile(String siteIdIn, Date date) {
        return Mono.just(siteIdIn)
            .doOnNext(siteId -> {
                GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfo(siteId);
                if (null == gwInfo) {
                    throw new DcArgumentException("控制器编号无效");
                }
                this.gtiMqService.uploadGtiDataFile(gwInfo, date, null);
            })
            .map(o -> Boolean.TRUE);

    }

    /**
     * 根据供应商获取设备信息映射
     *
     */
    public Map<String, GtiPo> getDeviceInfoMapByVendor(GtiVendor vendor, Integer code) {
        log.info("开始获取供应商{}的设备信息映射", vendor);
        List<GtiPo> deviceList = gtiRoDs.getByVendor(vendor, code);
        if (CollectionUtils.isEmpty(deviceList)) {
            log.warn("未找到供应商{}的设备信息", vendor);
            return Map.of();
        }

        Map<String, GtiPo> deviceMap = deviceList.stream()
                .filter(device -> StringUtils.isNotBlank(device.getSerialNo()))
                .collect(Collectors.toMap(GtiPo::getSerialNo, device -> device));

        log.info("获取到供应商{}的设备信息映射，设备数量: {}", vendor, deviceMap.size());
        return deviceMap;
    }

    /**
     * 采集并处理华为云设备日数据
     * 包含完整的流程：获取设备序列号 -> 调用华为云API -> 处理数据并入库
     */
    public Mono<String> collectAndProcessDeviceKpiDay(LocalDate collectDate) {
        long collectTime = collectDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        log.info("开始采集华为云设备日数据，采集日期: {}", collectDate);
        Map<String, GtiPo> deviceInfoMap = getDeviceInfoMapByVendor(GtiVendor.HUAWEI, EssEquipType.PV_INV.getCode());
        if (deviceInfoMap.isEmpty()) {
            String message = "未找到华为供应商的设备信息";
            log.warn(message);
            return Mono.just(message);
        }
        List<String> snsList = deviceInfoMap.keySet().stream().collect(Collectors.toList());
        int batchSize = 100;
        List<List<String>> batches = new ArrayList<>();
        for (int i = 0; i < snsList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, snsList.size());
            batches.add(snsList.subList(i, end));
        }
        log.info("设备总数: {}, 分批数量: {}, 每批大小: {}", snsList.size(), batches.size(), batchSize);
        return Flux.fromIterable(batches)
            .index()
            .concatMap(indexedBatch -> {
                long index = indexedBatch.getT1();
                List<String> batch = indexedBatch.getT2();
                String sns = String.join(",", batch);
                log.info("处理第 {}/{} 批，设备数量: {}", index + 1, batches.size(), batch.size());
                
                HwCloudDeviceKpiDayRequest request = new HwCloudDeviceKpiDayRequest();
                request.setSns(sns);
                request.setDevTypeId(1);
                request.setCollectTime(collectTime);
                
                return hwCloudClientService.getDevKpiDay(request)
                    .doOnNext(response -> {
                        if (response != null && response.isSuccess() && response.getData() != null) {
                            log.info("第 {}/{} 批数据采集成功，数据条数: {}", index + 1, batches.size(), response.getData().size());
                        } else {
                            log.warn("第 {}/{} 批数据采集失败或无数据", index + 1, batches.size());
                        }
                    })
                    .doOnError(e -> log.error("第 {}/{} 批数据采集异常: {}", index + 1, batches.size(), e.getMessage(), e))
                    .onErrorReturn(new HwCloudDeviceKpiDayResponse())
                    .map(response -> response != null && response.isSuccess() && response.getData() != null ? response.getData() : new ArrayList<HwCloudDeviceKpiDayResponse.HwCloudDeviceKpiDayData>());
            })
            .collectList()
            .map(listOfLists -> {
                List<HwCloudDeviceKpiDayResponse.HwCloudDeviceKpiDayData> allData = listOfLists.stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
                
                if (!allData.isEmpty()) {
                    processDeviceKpiDayDataWithMap(allData, deviceInfoMap);
                    String message = String.format("华为云设备日数据处理完成，总计处理 %d 条数据", allData.size());
                    log.info(message);
                    return message;
                } else {
                    String message = "所有批次均无有效数据";
                    log.warn(message);
                    return message;
                }
            });
    }

    /**
     * 使用设备信息映射处理设备日数据并保存到数据库
     */
    public void processDeviceKpiDayDataWithMap(List<HwCloudDeviceKpiDayResponse.HwCloudDeviceKpiDayData> deviceKpiDataList,
                                               Map<String, GtiPo> deviceInfoMap) {
        log.info("开始使用设备信息映射处理设备日数据，数据条数: {}，设备映射数量: {}",
                deviceKpiDataList != null ? deviceKpiDataList.size() : 0,
                deviceInfoMap != null ? deviceInfoMap.size() : 0);
        if (CollectionUtils.isEmpty(deviceKpiDataList)) {
            log.warn("设备日数据为空，无需处理");
            return;
        }
        if (CollectionUtils.isEmpty(deviceInfoMap)) {
            log.warn("设备信息映射为空，无法处理数据");
            return;
        }

        List<GtiDailyPo> gtiDailyPoList = new ArrayList<>();
        for (HwCloudDeviceKpiDayResponse.HwCloudDeviceKpiDayData data : deviceKpiDataList) {
            try {
                GtiPo gtiPo = deviceInfoMap.get(data.getSn());
                if (gtiPo == null) {
                    log.warn("设备信息映射中未找到序列号为 {} 的设备信息，跳过数据处理", data.getSn());
                    continue;
                }
                GtiDailyPo gtiDailyPo = new GtiDailyPo();
                gtiDailyPo.setDate(new Date(data.getCollectTime()));
                gtiDailyPo.setGtiId(gtiPo.getId());
                gtiDailyPo.setDno(data.getSn());
                gtiDailyPo.setSiteId(gtiPo.getSiteId());
                Map<String, Object> dataItemMap = data.getDataItemMap();
                if (dataItemMap != null) {
                    Object productPower = dataItemMap.get("product_power");// 当日发电量 (product_power)
                    if (productPower != null) {
                        gtiDailyPo.setTodayKwh(new BigDecimal(productPower.toString()));
                    }
                }
                gtiDailyPoList.add(gtiDailyPo);
            } catch (Exception e) {
                log.error("处理设备 {} 日数据时发生异常: {}", data.getSn(), e.getMessage(), e);
            }
        }
        if (!gtiDailyPoList.isEmpty()) {
            gtiDailyRwDs.batchUpsetGtiDaily(gtiDailyPoList);
            log.info("使用设备信息映射处理设备日数据完成，成功处理 {} 条数据", gtiDailyPoList.size());
        } else {
            log.warn("没有有效的GTI日数据需要插入");
        }
    }
    
    /**
     * 采集和处理设备实时数据
     * @param collectTime 任务执行时间（采集时间）
     */
    public Mono<String> collectAndProcessDeviceRealKpi(LocalDateTime collectTime) {
        log.info("开始采集华为云设备实时数据，采集时间: {}", collectTime);
        Map<String, GtiPo> deviceInfoMap = getDeviceInfoMapByVendor(GtiVendor.HUAWEI, EssEquipType.PV_INV.getCode());
        if (deviceInfoMap.isEmpty()) {
            String message = "未找到华为供应商的设备信息";
            log.warn(message);
            return Mono.just(message);
        }

        List<String> snsList = deviceInfoMap.keySet().stream().collect(Collectors.toList());
        int batchSize = 100;
        List<List<String>> batches = new ArrayList<>();
        for (int i = 0; i < snsList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, snsList.size());
            batches.add(snsList.subList(i, end));
        }

        log.info("设备总数: {}, 分批数量: {}, 每批大小: {}", snsList.size(), batches.size(), batchSize);

        return Flux.fromIterable(batches)
            .index()
            .concatMap(indexedBatch -> {
                long index = indexedBatch.getT1();
                List<String> batch = indexedBatch.getT2();
                String sns = String.join(",", batch);
                log.info("处理第 {}/{} 批，设备数量: {}", index + 1, batches.size(), batch.size());
                
                HwCloudDeviceRealKpiRequest request = new HwCloudDeviceRealKpiRequest();
                request.setSns(sns);
                request.setDevTypeId(1); // 逆变器设备类型ID
                
                return hwCloudClientService.getDevRealKpi(request)
                    .doOnNext(response -> {
                        if (response != null && response.isSuccess() && response.getData() != null) {
                            log.info("第 {}/{} 批实时数据采集成功，数据条数: {}", index + 1, batches.size(), response.getData().size());
                        } else {
                            log.warn("第 {}/{} 批实时数据采集失败或无数据", index + 1, batches.size());
                        }
                    })
                    .doOnError(e -> log.error("第 {}/{} 批实时数据采集异常: {}", index + 1, batches.size(), e.getMessage(), e))
                    .onErrorReturn(new HwCloudDeviceRealKpiResponse());
            })
            .collectList()
            .map(responses -> {
                List<HwCloudDeviceRealKpiResponse.HwCloudDeviceRealKpiData> allData = new ArrayList<>();
                Long serverTime = null; // 华为云服务器时间
                
                for (HwCloudDeviceRealKpiResponse response : responses) {
                    if (response != null && response.isSuccess() && response.getData() != null) {
                        allData.addAll(response.getData());
                        // 获取华为云服务器时间（只需要获取一次）
                        if (serverTime == null && response.getParams() != null) {
                            serverTime = response.getParams().getCurrentTime();
                        }
                    }
                }
                
                if (!allData.isEmpty()) {
                    processDeviceRealKpiData(allData, deviceInfoMap, collectTime, serverTime);
                    String message = String.format("华为云设备实时数据处理完成，总计处理 %d 条数据", allData.size());
                    log.info(message);
                    return message;
                } else {
                    String message = "所有批次均无有效实时数据";
                    log.warn(message);
                    return message;
                }
            });
    }

    /**
     * 处理设备实时数据
     */
    private void processDeviceRealKpiData(List<HwCloudDeviceRealKpiResponse.HwCloudDeviceRealKpiData> deviceRealKpiDataList,
                                          Map<String, GtiPo> deviceInfoMap, LocalDateTime collectTime, Long serverTime) {
        log.info("开始处理设备实时数据，数据条数: {}，设备映射数量: {}",
                deviceRealKpiDataList != null ? deviceRealKpiDataList.size() : 0,
                deviceInfoMap != null ? deviceInfoMap.size() : 0);
        if (CollectionUtils.isEmpty(deviceRealKpiDataList)) {
            log.warn("设备实时数据为空，无需处理");
            return;
        }
        if (CollectionUtils.isEmpty(deviceInfoMap)) {
            log.warn("设备信息映射为空，无法处理数据");
            return;
        }
        
        // 收集所有需要保存的时序数据
        List<TsPvGtiPo> pvGtiPoList = new ArrayList<>();
        List<TsPvStringPo> pvStringPoList = new ArrayList<>();
        
        for (HwCloudDeviceRealKpiResponse.HwCloudDeviceRealKpiData data : deviceRealKpiDataList) {
            try {
                GtiPo gtiPo = deviceInfoMap.get(data.getSn());
                if (gtiPo == null) {
                    log.warn("设备信息映射中未找到序列号为 {} 的设备信息，跳过数据处理", data.getSn());
                    continue;
                }
                Map<String, Object> dataItemMap = data.getDataItemMap();
                if (dataItemMap != null) {
                    // 转换为时序库数据模型
                    TsPvGtiPo pvGtiPo = convertToPvGtiPo(data.getSn(), dataItemMap, collectTime, serverTime);
                    if (pvGtiPo != null) {
                        pvGtiPoList.add(pvGtiPo);
                    }
                    
                    // 提取组串数据
                    List<TsPvStringPo> stringPoList = convertToPvStringPoList(data.getSn(), dataItemMap, collectTime, serverTime);
                    if (!stringPoList.isEmpty()) {
                        pvStringPoList.addAll(stringPoList);
                    }
                }
            } catch (Exception e) {
                log.error("处理设备 {} 实时数据时发生异常: {}", data.getSn(), e.getMessage(), e);
            }
        }
        
        // 分批保存光伏逆变器数据
        if (!pvGtiPoList.isEmpty()) {
            batchSavePvGtiData(pvGtiPoList);
        }
        
        // 分批保存组串时序数据
        if (!pvStringPoList.isEmpty()) {
            batchSavePvStringData(pvStringPoList);
        }
        
        log.info("设备实时数据处理完成，成功处理 {} 条数据", deviceRealKpiDataList.size());
    }

    /**
     * 分批保存光伏逆变器数据
     */
    private void batchSavePvGtiData(List<TsPvGtiPo> pvGtiPoList) {
        int batchSize = 1000; // 每批1000条，避免请求体过大
        int totalSize = pvGtiPoList.size();
        
        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<TsPvGtiPo> batchData = pvGtiPoList.subList(i, endIndex);

            int finalI = i;
            essFeignClient.batchSavePvGtiData(batchData)
                .doOnSuccess(response -> {
                    if (response != null && response.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                        log.info("批量保存设备实时数据到时序库成功，批次: {}/{}, 数量: {}", 
                            (finalI / batchSize + 1), (totalSize + batchSize - 1) / batchSize, batchData.size());
                    } else {
                        log.error("批量保存设备实时数据到时序库失败: {}", response.getError());
                    }
                })
                .doOnError(error -> log.error("批量保存设备实时数据到时序库失败: {}", error.getMessage()))
                .subscribe();
        }
        
        log.info("光伏逆变器数据分批保存完成，总数量: {}", totalSize);
    }

    /**
     * 分批保存组串数据
     */
    private void batchSavePvStringData(List<TsPvStringPo> pvStringPoList) {
        int batchSize = 1000; // 每批1000条，避免请求体过大
        int totalSize = pvStringPoList.size();
        
        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<TsPvStringPo> batchData = pvStringPoList.subList(i, endIndex);

            int finalI = i;
            essFeignClient.batchSavePvStringData(batchData)
                .doOnSuccess(response -> {
                    if (response != null && response.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                        log.info("批量保存组串实时数据到时序库成功，批次: {}/{}, 数量: {}", 
                            (finalI / batchSize + 1), (totalSize + batchSize - 1) / batchSize, batchData.size());
                    } else {
                        log.error("批量保存组串实时数据到时序库失败: {}", response.getError());
                    }
                })
                .doOnError(error -> log.error("批量保存组串实时数据到时序库失败: {}", error.getMessage()))
                .subscribe();
        }
        
        log.info("组串数据分批保存完成，总数量: {}", totalSize);
    }

    /**
     * 将华为云实时数据转换为时序库数据模型
     */
    private TsPvGtiPo convertToPvGtiPo(String deviceSn, Map<String, Object> dataItemMap, LocalDateTime collectTime, Long serverTime) {
        if (dataItemMap == null || dataItemMap.isEmpty()) {
            return null;
        }

        try {
            TsPvGtiPo pvGtiPo = new TsPvGtiPo();
            
            // 设备编号和时间
            pvGtiPo.setDno(deviceSn);
            pvGtiPo.setTs(collectTime); // 采集时间 - 任务执行时传入的时间
            pvGtiPo.setSts(serverTime != null ? 
                LocalDateTime.ofInstant(java.time.Instant.ofEpochMilli(serverTime), ZoneId.systemDefault()) : 
                LocalDateTime.now()); // 服务器时间 - 华为接口返回的currentTime
            
            // 交流侧电压 (V)
            pvGtiPo.setUab(parseDouble(dataItemMap.get("ab_u")));
            pvGtiPo.setUbc(parseDouble(dataItemMap.get("bc_u")));
            pvGtiPo.setUca(parseDouble(dataItemMap.get("ca_u")));
            pvGtiPo.setUa(parseDouble(dataItemMap.get("a_u")));
            pvGtiPo.setUb(parseDouble(dataItemMap.get("b_u")));
            pvGtiPo.setUc(parseDouble(dataItemMap.get("c_u")));
            
            // 交流侧电流 (A)
            pvGtiPo.setIa(parseDouble(dataItemMap.get("a_i")));
            pvGtiPo.setIb(parseDouble(dataItemMap.get("b_i")));
            pvGtiPo.setIc(parseDouble(dataItemMap.get("c_i")));
            
            // 频率 (Hz) - 华为云只有一个电网频率，映射到A、B、C相
            Double elecFreq = parseDouble(dataItemMap.get("elec_freq"));
            pvGtiPo.setFa(elecFreq);
            pvGtiPo.setFb(elecFreq);
            pvGtiPo.setFc(elecFreq);
            
            // 功率与电能质量
            pvGtiPo.setP(parseDouble(dataItemMap.get("active_power"))); // 有功功率 (kW)
            pvGtiPo.setQ(parseDouble(dataItemMap.get("reactive_power"))); // 无功功率 (kVar)
            // 计算视在功率 S = sqrt(P^2 + Q^2)
            Double p = pvGtiPo.getP();
            Double q = pvGtiPo.getQ();
            if (p != null && q != null) {
                pvGtiPo.setS(Math.sqrt(p * p + q * q));
            }
            pvGtiPo.setPf(parseDouble(dataItemMap.get("power_factor"))); // 功率因数
            pvGtiPo.setF(parseDouble(dataItemMap.get("elec_freq"))); // 电网频率 (Hz)
            
            // 直流侧
            pvGtiPo.setIp(parseDouble(dataItemMap.get("mppt_power"))); // MPPT输入总功率 (kW)
            
            // 整体性能与状态
            pvGtiPo.setTe(parseDouble(dataItemMap.get("efficiency"))); // 转换效率 (%)
            pvGtiPo.setIt(parseDouble(dataItemMap.get("temperature"))); // 内部温度 (°C)
            pvGtiPo.setRs(parseInteger(dataItemMap.get("run_state"))); // 运行状态
            
            // 发电量统计
            pvGtiPo.setEpe(parseDouble(dataItemMap.get("total_cap"))); // 累计发电量 (kWh)
            // 累计发电时间 - 华为云接口没有提供，设置为null
            pvGtiPo.setAh(null);
            pvGtiPo.setTk(parseDouble(dataItemMap.get("day_cap"))); // 当日发电量 (kWh)
            
            return pvGtiPo;
        } catch (Exception e) {
            log.error("转换设备 {} 数据为时序库模型失败: {}", deviceSn, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 将华为云实时数据转换为组串时序库数据模型列表
     */
    private List<TsPvStringPo> convertToPvStringPoList(String deviceSn, Map<String, Object> dataItemMap, LocalDateTime collectTime, Long serverTime) {
        List<TsPvStringPo> stringPoList = new ArrayList<>();
        
        if (dataItemMap == null || dataItemMap.isEmpty()) {
            return stringPoList;
        }
        
        try {
            // 遍历pv1_u到pv36_u和pv1_i到pv36_i
            for (int i = 1; i <= 36; i++) {
                String voltageKey = "pv" + i + "_u";
                String currentKey = "pv" + i + "_i";
                
                Double voltage = parseDouble(dataItemMap.get(voltageKey));
                Double current = parseDouble(dataItemMap.get(currentKey));
                
                // 只有当电压或电流有值时才创建组串数据
                if (voltage != null || current != null) {
                    TsPvStringPo pvStringPo = new TsPvStringPo();
                    
                    // 设备编号和时间
                    pvStringPo.setDno(deviceSn);
                    pvStringPo.setTs(collectTime); // 采集时间
                    pvStringPo.setSts(serverTime != null ? 
                        LocalDateTime.ofInstant(java.time.Instant.ofEpochMilli(serverTime), ZoneId.systemDefault()) : 
                        LocalDateTime.now()); // 服务器时间
                    
                    // 组串数据
                    pvStringPo.setIdx(i); // 组串ID，对应pv后缀的数字
                    pvStringPo.setU(voltage); // 直流电压 (V)
                    pvStringPo.setI(current); // 直流电流 (A)
                    
                    stringPoList.add(pvStringPo);
                }
            }
            
//            log.debug("设备 {} 提取到 {} 个组串数据", deviceSn, stringPoList.size());
            return stringPoList;
        } catch (Exception e) {
            log.error("转换设备 {} 组串数据失败: {}", deviceSn, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 安全解析Double值
     */
    private Double parseDouble(Object value) {
        if (value == null || "N/A".equals(value)) {
            return null;
        }
        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 安全解析Integer值
     */
    private Integer parseInteger(Object value) {
        if (value == null || "N/A".equals(value)) {
            return null;
        }
        try {
            if (value instanceof Number) {
                return ((Number) value).intValue();
            }
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
